{"version": 3, "file": "parse.js", "sourceRoot": "", "sources": ["../../src/parse.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AACzC,OAAO,EAEL,QAAQ,EAER,UAAU,IAAI,eAAe,GAC9B,MAAM,YAAY,CAAC;AAGpB;;;;;GAKG;AACH,MAAM,UAAU,QAAQ,CACtB,MAKa;IAEb;;;;;;;;OAQG;IACH,OAAO,SAAS,KAAK,CACnB,OAAyD,EACzD,OAAwB,EACxB,UAAmB,EACnB,OAA0B;QAE1B,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9D,OAAO,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC/B,CAAC;QAED,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,GAAG,GAAG,OAAyC,CAAC;QAEtD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;YAChD,6CAA6C;YAC7C,OAAO,GAAG,CAAC;QACb,CAAC;QAED,iCAAiC;QACjC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC;QAE9B,gCAAgC;QAChC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAElB,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,MAAM,CACpB,SAA8B,EAC9B,MAAyB;IAEzB,YAAY;IACZ,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAE/D,gBAAgB;IAChB,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC;IACxB,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,IAAI,CAAC;IAChB,CAAC;IAED,mBAAmB;IACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACpC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAEpB,gEAAgE;QAChE,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;YAChD,aAAa,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC;YAC/B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC"}