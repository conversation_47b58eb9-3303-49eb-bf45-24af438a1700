# Class: ClientStats

Stats for a [Client](/docs/docs/api/Client.md).

## `new ClientStats(client)`

Arguments:

* **client** `Client` - Client from which to return stats.

## Instance Properties

### `ClientStats.connected`

Boolean if socket as open connection by this client.

### `ClientStats.pending`

Number of pending requests of this client.

### `ClientStats.running`

Number of currently active requests across this client.

### `ClientStats.size`

Number of active, pending, or queued requests of this clients.
