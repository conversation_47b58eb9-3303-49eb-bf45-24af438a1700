{"version": 3, "file": "compile.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["compile.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qCAAyD;AACzD,sDAAgC;AAChC,mDAAmD;AACnD,2CAAsD;AACtD,kEAG0C;AAO1C;;;;;;GAMG;AACH,SAAgB,OAAO,CACnB,QAA+B,EAC/B,OAA2C,EAC3C,OAAuB;IAEvB,IAAM,IAAI,GAAG,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACvD,OAAO,IAAA,2BAAW,EAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AAC9C,CAAC;AAPD,0BAOC;AAED,SAAgB,aAAa,CACzB,QAA+B,EAC/B,OAA2C,EAC3C,OAAuB;IAEvB,IAAM,KAAK,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IACxE,OAAO,YAAY,CAAoB,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACpE,CAAC;AAPD,sCAOC;AAED,SAAS,mBAAmB,CAAC,CAAmB;IAC5C,OAAO,CACH,CAAC,CAAC,IAAI,KAAK,uBAAY,CAAC,MAAM;QAC9B,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO;YACf,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;gBAClB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAA9B,CAA8B,CAAC,CAAC,CAAC,CAClE,CAAC;AACN,CAAC;AAED,IAAM,gBAAgB,GAAa,EAAE,IAAI,EAAE,uBAAY,CAAC,UAAU,EAAE,CAAC;AACrE,IAAM,yBAAyB,GAAqB;IAChD,IAAI,EAAE,qBAAqB;CAC9B,CAAC;AACF,IAAM,WAAW,GAAa;IAC1B,IAAI,EAAE,uBAAY,CAAC,MAAM;IACzB,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,IAAI;CACb,CAAC;AAEF;;;GAGG;AACH,SAAS,UAAU,CACf,KAA2B,EAC3B,EAA+C,EAC/C,OAAgB;QADd,OAAO,aAAA;IAGT,qDAAqD;IACrD,IAAM,UAAU,GAAG,CAAC,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,CAAC,UAAC,CAAC;QAClC,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACxD,OAAO,CAAC,KAAK,mCAAmB,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAA,CAAC;IAEH,KAAgB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;QAAlB,IAAM,CAAC,cAAA;QACR,IACI,CAAC,CAAC,MAAM,GAAG,CAAC;YACZ,IAAA,qBAAW,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,uBAAY,CAAC,UAAU,EACvC;YACE,gCAAgC;SACnC;aAAM,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE;YACnD,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;SAC/B;aAAM;YACH,SAAS;SACZ;QAED,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;KAC1B;AACL,CAAC;AAED,SAAgB,YAAY,CACxB,KAA2B,EAC3B,OAA2C,EAC3C,OAAuB;;IAEvB,KAAK,CAAC,OAAO,CAAC,iBAAS,CAAC,CAAC;IAEzB,OAAO,GAAG,MAAA,OAAO,CAAC,OAAO,mCAAI,OAAO,CAAC;IACrC,IAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAE9C,IAAM,YAAY,GACd,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IAE9D,oCAAoC;IACpC,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,EAAE;QACpC,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;KAC5C;SAAM,IAAI,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,IAAA,qBAAW,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAjC,CAAiC,CAAC,EAAE;QAC7D,MAAM,IAAI,KAAK,CACX,mFAAmF,CACtF,CAAC;KACL;IAED,IAAI,sBAAsB,GAAG,KAAK,CAAC;IAEnC,IAAM,KAAK,GAAG,KAAK;SACd,GAAG,CAAC,UAAC,KAAK;QACP,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;YACZ,IAAA,KAAK,GAAY,KAAK,GAAjB,EAAE,MAAM,GAAI,KAAK,GAAT,CAAU;YAE9B,IACI,KAAK,CAAC,IAAI,KAAK,uBAAY,CAAC,MAAM;gBAClC,KAAK,CAAC,IAAI,KAAK,OAAO,EACxB;gBACE,SAAS;aACZ;iBAAM,IACH,cAAc;gBACd,MAAM,CAAC,IAAI,KAAK,uBAAY,CAAC,UAAU,EACzC;gBACE,KAAK,CAAC,CAAC,CAAC,GAAG,yBAAyB,CAAC;aACxC;iBAAM,IACH,MAAM,CAAC,IAAI,KAAK,uBAAY,CAAC,QAAQ;gBACrC,MAAM,CAAC,IAAI,KAAK,uBAAY,CAAC,OAAO,EACtC;gBACE,sBAAsB,GAAG,IAAI,CAAC;aACjC;SACJ;QAED,OAAO,YAAY,CACf,KAAK,EACL,OAAO,EACP,YAAY,CACf,CAAC;IACN,CAAC,CAAC;SACD,MAAM,CAAC,WAAW,EAAE,kBAAQ,CAAC,SAAS,CAAC,CAAC;IAE7C,KAAK,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;IAEtD,OAAO,KAAK,CAAC;AACjB,CAAC;AA1DD,oCA0DC;AAED,SAAS,YAAY,CACjB,KAAyB,EACzB,OAA2C,EAC3C,OAAgB;;IAEhB,OAAO,KAAK,CAAC,MAAM,CACf,UAAC,QAAQ,EAAE,IAAI;QACX,OAAA,QAAQ,KAAK,kBAAQ,CAAC,SAAS;YAC3B,CAAC,CAAC,kBAAQ,CAAC,SAAS;YACpB,CAAC,CAAC,IAAA,mCAAsB,EAClB,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,OAAO,EACP,YAAY,CACf;IARP,CAQO,EACX,MAAA,OAAO,CAAC,QAAQ,mCAAI,kBAAQ,CAAC,QAAQ,CACxC,CAAC;AACN,CAAC;AAED,SAAS,WAAW,CAChB,CAA6B,EAC7B,CAA6B;IAE7B,IAAI,CAAC,KAAK,kBAAQ,CAAC,SAAS,IAAI,CAAC,KAAK,kBAAQ,CAAC,QAAQ,EAAE;QACrD,OAAO,CAAC,CAAC;KACZ;IACD,IAAI,CAAC,KAAK,kBAAQ,CAAC,SAAS,IAAI,CAAC,KAAK,kBAAQ,CAAC,QAAQ,EAAE;QACrD,OAAO,CAAC,CAAC;KACZ;IAED,OAAO,SAAS,OAAO,CAAC,IAAI;QACxB,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC;AACN,CAAC"}