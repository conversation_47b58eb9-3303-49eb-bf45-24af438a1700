import type { CompiledQuery, InternalOptions } from "./types.js";
import type { AttributeSelector, AttributeAction } from "css-what";
/**
 * Attribute selectors
 */
export declare const attributeRules: Record<AttributeAction, <Node, ElementNode extends Node>(next: CompiledQuery<ElementNode>, data: AttributeSelector, options: InternalOptions<Node, ElementNode>) => CompiledQuery<ElementNode>>;
//# sourceMappingURL=attributes.d.ts.map