{"name": "parse5-htmlparser2-tree-adapter", "type": "module", "description": "htmlparser2 tree adapter for parse5.", "version": "7.1.0", "author": "<PERSON> <<EMAIL>> (https://github.com/inikulin)", "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://parse5.js.org", "funding": "https://github.com/inikulin/parse5?sponsor=1", "keywords": ["parse5", "parser", "tree adapter", "htmlparser2"], "license": "MIT", "main": "dist/cjs/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "dependencies": {"domhandler": "^5.0.3", "parse5": "^7.0.0"}, "scripts": {"build:cjs": "tsc --moduleResolution node10 --module CommonJS --target ES6 --outDir dist/cjs && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json"}, "repository": {"type": "git", "url": "git://github.com/inikulin/parse5.git"}, "files": ["dist/cjs/package.json", "dist/**/*.js", "dist/**/*.d.ts"]}