hoistPattern:
  - '*'
hoistedDependencies:
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  arg@4.1.3:
    arg: private
  asynckit@0.4.0:
    asynckit: private
  boolbase@1.0.0:
    boolbase: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  cheerio-select@2.1.0:
    cheerio-select: private
  combined-stream@1.0.8:
    combined-stream: private
  create-require@1.1.1:
    create-require: private
  css-select@5.2.2:
    css-select: private
  css-what@6.2.2:
    css-what: private
  delayed-stream@1.0.0:
    delayed-stream: private
  diff@4.0.2:
    diff: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dunder-proto@1.0.1:
    dunder-proto: private
  encoding-sniffer@0.2.1:
    encoding-sniffer: private
  entities@4.5.0:
    entities: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  follow-redirects@1.15.9:
    follow-redirects: private
  form-data@4.0.4:
    form-data: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  gopd@1.2.0:
    gopd: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  htmlparser2@10.0.0:
    htmlparser2: private
  iconv-lite@0.6.3:
    iconv-lite: private
  make-error@1.3.6:
    make-error: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  nth-check@2.1.1:
    nth-check: private
  parse5-htmlparser2-tree-adapter@7.1.0:
    parse5-htmlparser2-tree-adapter: private
  parse5-parser-stream@7.1.2:
    parse5-parser-stream: private
  parse5@7.3.0:
    parse5: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  safer-buffer@2.1.2:
    safer-buffer: private
  undici-types@7.8.0:
    undici-types: private
  undici@7.12.0:
    undici: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  yn@3.1.1:
    yn: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.13.1
pendingBuilds: []
prunedAt: Sun, 20 Jul 2025 20:41:48 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
