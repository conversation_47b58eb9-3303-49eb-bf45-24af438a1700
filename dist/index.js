"use strict";
// Welcome to your new TypeScript project!
function greet(name) {
    return `Hello, ${name}! Welcome to TypeScript with pnpm.`;
}
function main() {
    const message = greet("World");
    console.log(message);
    // Example of TypeScript features
    const numbers = [1, 2, 3, 4, 5];
    const doubled = numbers.map((n) => n * 2);
    console.log("Original numbers:", numbers);
    console.log("Doubled numbers:", doubled);
}
// Run the main function
main();
