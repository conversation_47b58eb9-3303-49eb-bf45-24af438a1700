import * as fs from 'fs/promises';
import * as path from 'path';
import { GroupAnalysis, ApartmentAnalysis, LocationGroup } from '../types/apartment';

export class ReportGenerator {
  private outputDir = 'output';

  async generateReport(analysis: GroupAnalysis): Promise<void> {
    // Ensure output directory exists
    await this.ensureOutputDirectory();

    // Generate different report formats
    await Promise.all([
      this.generateSummaryReport(analysis),
      this.generateDetailedReport(analysis),
      this.generateCSVReport(analysis),
      this.generateGroupStatistics(analysis)
    ]);

    console.log(`📁 Reports generated in ./${this.outputDir}/ directory`);
  }

  private async ensureOutputDirectory(): Promise<void> {
    try {
      await fs.access(this.outputDir);
    } catch {
      await fs.mkdir(this.outputDir, { recursive: true });
    }
  }

  private async generateSummaryReport(analysis: GroupAnalysis): Promise<void> {
    const timestamp = new Date().toISOString().split('T')[0];
    let report = `# Belgrade Apartment Analysis Summary\n`;
    report += `Generated: ${new Date().toLocaleString()}\n\n`;

    // Overall statistics
    const totalApartments = Array.from(analysis.groups.values())
      .reduce((sum, group) => sum + group.apartmentCount, 0);
    
    const totalUndervalued = analysis.overallTopUndervalued.length;
    
    report += `## 📊 Overall Statistics\n`;
    report += `- Total apartments analyzed: ${totalApartments}\n`;
    report += `- Total undervalued apartments found: ${totalUndervalued}\n`;
    report += `- Number of location groups: ${analysis.groups.size}\n\n`;

    // Top 50 overall undervalued for summary (full 200 in detailed report)
    report += `## 🏆 Top 50 Most Undervalued Apartments Overall\n\n`;

    for (let i = 0; i < Math.min(50, analysis.overallTopUndervalued.length); i++) {
      const item = analysis.overallTopUndervalued[i];
      report += this.formatApartmentSummary(item, i + 1);
    }

    // Top undervalued by group
    report += `\n## 📍 Top Undervalued by Location Group\n\n`;
    
    for (const [groupName, groupAnalyses] of analysis.topUndervaluedByGroup) {
      report += `### ${groupName}\n\n`;
      
      for (let i = 0; i < Math.min(10, groupAnalyses.length); i++) {
        const item = groupAnalyses[i];
        report += this.formatApartmentSummary(item, i + 1, false);
      }
      report += '\n';
    }

    await fs.writeFile(
      path.join(this.outputDir, `apartment-analysis-summary-${timestamp}.md`),
      report
    );
  }

  private async generateDetailedReport(analysis: GroupAnalysis): Promise<void> {
    const timestamp = new Date().toISOString().split('T')[0];
    let report = `# Belgrade Apartment Analysis - Detailed Report\n`;
    report += `Generated: ${new Date().toLocaleString()}\n\n`;

    // Detailed group analysis
    report += `## 📊 Location Group Analysis\n\n`;
    
    for (const [groupName, group] of analysis.groups) {
      report += `### ${groupName}\n`;
      report += `- Apartments: ${group.apartmentCount}\n`;
      report += `- Average price/m²: €${group.averagePricePerSqm}\n`;
      report += `- Median price/m²: €${group.medianPricePerSqm}\n`;
      
      if (group.subGroups && group.subGroups.size > 0) {
        report += `\n#### Subgroups:\n`;
        for (const [subGroupName, subGroup] of group.subGroups) {
          report += `- **${subGroupName}**: ${subGroup.apartmentCount} apartments, `;
          report += `avg: €${subGroup.averagePricePerSqm}/m², median: €${subGroup.medianPricePerSqm}/m²\n`;
        }
      }
      report += '\n';
    }

    // All undervalued apartments with full details
    report += `## 🏠 All Undervalued Apartments\n\n`;
    
    for (const item of analysis.overallTopUndervalued) {
      report += this.formatApartmentDetailed(item);
    }

    await fs.writeFile(
      path.join(this.outputDir, `apartment-analysis-detailed-${timestamp}.md`),
      report
    );
  }

  private async generateCSVReport(analysis: GroupAnalysis): Promise<void> {
    const timestamp = new Date().toISOString().split('T')[0];
    
    // CSV header
    let csv = 'Rank,Title,Price,Size,PricePerSqm,MainGroup,SubGroup,MainGroupRating,SubGroupRating,Location,URL\n';
    
    // Add all undervalued apartments
    analysis.overallTopUndervalued.forEach((item, index) => {
      const apt = item.apartment;
      csv += `${index + 1},"${apt.title.replace(/"/g, '""')}",${apt.price},${apt.size},${apt.pricePerSqm},`;
      csv += `"${item.mainGroupName}","${item.subGroupName}",${item.mainGroupRating},${item.subGroupRating},`;
      csv += `"${apt.fullLocation.replace(/"/g, '""')}","${apt.url}"\n`;
    });

    await fs.writeFile(
      path.join(this.outputDir, `apartment-analysis-${timestamp}.csv`),
      csv
    );
  }

  private async generateGroupStatistics(analysis: GroupAnalysis): Promise<void> {
    const timestamp = new Date().toISOString().split('T')[0];
    
    const stats = {
      generatedAt: new Date().toISOString(),
      totalGroups: analysis.groups.size,
      totalApartments: Array.from(analysis.groups.values()).reduce((sum, group) => sum + group.apartmentCount, 0),
      totalUndervalued: analysis.overallTopUndervalued.length,
      groups: {} as any
    };

    // Add group statistics
    for (const [groupName, group] of analysis.groups) {
      stats.groups[groupName] = {
        apartmentCount: group.apartmentCount,
        averagePricePerSqm: group.averagePricePerSqm,
        medianPricePerSqm: group.medianPricePerSqm,
        subGroups: {} as any
      };

      if (group.subGroups) {
        for (const [subGroupName, subGroup] of group.subGroups) {
          stats.groups[groupName].subGroups[subGroupName] = {
            apartmentCount: subGroup.apartmentCount,
            averagePricePerSqm: subGroup.averagePricePerSqm,
            medianPricePerSqm: subGroup.medianPricePerSqm
          };
        }
      }
    }

    await fs.writeFile(
      path.join(this.outputDir, `apartment-statistics-${timestamp}.json`),
      JSON.stringify(stats, null, 2)
    );
  }

  private formatApartmentSummary(item: ApartmentAnalysis, rank: number, showRank: boolean = true): string {
    const apt = item.apartment;
    let summary = showRank ? `${rank}. ` : '- ';
    
    summary += `**€${apt.price.toLocaleString()}** (€${apt.pricePerSqm}/m²) - `;
    summary += `${apt.size}m² in ${item.subGroupName}\n`;
    summary += `   📍 ${apt.fullLocation}\n`;
    summary += `   📊 ${item.subGroupRating.toFixed(1)}% below subgroup avg, `;
    summary += `${item.mainGroupRating.toFixed(1)}% below main group avg\n`;
    summary += `   🔗 [View listing](${apt.url})\n\n`;
    
    return summary;
  }

  private formatApartmentDetailed(item: ApartmentAnalysis): string {
    const apt = item.apartment;
    let details = `### ${apt.title}\n\n`;
    
    details += `- **Price**: €${apt.price.toLocaleString()}\n`;
    details += `- **Size**: ${apt.size}m²\n`;
    details += `- **Price per m²**: €${apt.pricePerSqm}\n`;
    details += `- **Location**: ${apt.fullLocation}\n`;
    details += `- **Floor**: ${apt.floor}\n`;
    details += `- **Main Group**: ${item.mainGroupName} (${item.mainGroupRating.toFixed(1)}% vs avg)\n`;
    details += `- **Sub Group**: ${item.subGroupName} (${item.subGroupRating.toFixed(1)}% vs avg)\n`;
    details += `- **URL**: ${apt.url}\n\n`;
    
    if (apt.description) {
      details += `**Description**: ${apt.description}\n\n`;
    }
    
    details += '---\n\n';
    
    return details;
  }
}
