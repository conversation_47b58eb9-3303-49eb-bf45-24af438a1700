import axios from 'axios';
import * as cheerio from 'cheerio';

async function checkAvailability() {
  console.log('🔍 Checking apartment availability on Halo Oglasi...');
  
  for (let page = 1; page <= 5; page++) {
    try {
      const url = `https://www.halooglasi.com/nekretnine/prodaja-stanova/beograd?page=${page}`;
      console.log(`\n📄 Checking page ${page}: ${url}`);
      
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      
      // Count different types of links
      const allLinks = $('a').length;
      const apartmentLinks = $('a[href*="/nekretnine/prodaja-stanova/"]').length;
      const kidLinks = $('a[href*="/nekretnine/prodaja-stanova/"][href*="?kid="]').length;
      
      console.log(`   Total links: ${allLinks}`);
      console.log(`   Apartment links: ${apartmentLinks}`);
      console.log(`   Individual apartment links (?kid=): ${kidLinks}`);
      
      // Check for pagination info
      const paginationText = $('.pagination, .pager, [class*="page"]').text();
      if (paginationText) {
        console.log(`   Pagination info: ${paginationText.replace(/\s+/g, ' ').trim()}`);
      }
      
      // Check if page has content
      const bodyText = $('body').text();
      if (bodyText.includes('Nema rezultata') || bodyText.includes('No results')) {
        console.log(`   ❌ No results found on page ${page}`);
        break;
      }
      
      // Look for price patterns to see if apartments are there
      const priceMatches = bodyText.match(/\d+[.,]\d+\s*€/g);
      console.log(`   Price patterns found: ${priceMatches ? priceMatches.length : 0}`);
      
      if (kidLinks === 0) {
        console.log(`   ⚠️  No individual apartment links found on page ${page}`);
      }
      
      // Delay between requests
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`❌ Error checking page ${page}:`, error);
    }
  }
}

checkAvailability();
