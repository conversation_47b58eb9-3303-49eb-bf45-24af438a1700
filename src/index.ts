// Welcome to your new TypeScript project!

function greet(name: string): string {
  return `Hello, ${name}! Welcome to TypeScript with pnpm.`;
}

function main(): void {
  const message = greet("World");
  console.log(message);
  
  // Example of TypeScript features
  const numbers: number[] = [1, 2, 3, 4, 5];
  const doubled = numbers.map((n: number) => n * 2);
  
  console.log("Original numbers:", numbers);
  console.log("Doubled numbers:", doubled);
}

// Run the main function
main();
