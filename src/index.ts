import { HaloOglasiScraper } from './scraper/halo-oglasi-scraper';
import { ApartmentAnalyzer } from './analyzer/apartment-analyzer';
import { ReportGenerator } from './reports/report-generator';

async function main(): Promise<void> {
  console.log('🏠 Starting Belgrade Apartment Scanner...');

  try {
    // Initialize scraper for comprehensive analysis
    const scraper = new HaloOglasiScraper({
      maxPages: 20, // Scan enough pages to get good data without overwhelming
      delayBetweenRequests: 1500, // Be respectful to the server
      minSize: 50
    });

    // Scrape apartments
    console.log('📡 Scraping apartments from Halo Oglasi...');
    const apartments = await scraper.scrapeApartments();
    console.log(`✅ Found ${apartments.length} apartments`);

    // Analyze apartments
    console.log('📊 Analyzing apartments...');
    const analyzer = new ApartmentAnalyzer();
    const analysis = analyzer.analyzeApartments(apartments);

    // Generate report
    console.log('📝 Generating report...');
    const reportGenerator = new ReportGenerator();
    await reportGenerator.generateReport(analysis);

    console.log('🎉 Analysis complete! Check the output files.');

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

// Run the main function
main();
