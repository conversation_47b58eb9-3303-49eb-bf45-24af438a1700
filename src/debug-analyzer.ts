import { HaloOglasiScraper } from './scraper/halo-oglasi-scraper';

async function debugAnalyzer() {
  console.log('🔍 Debugging apartment data...');
  
  try {
    const scraper = new HaloOglasiScraper({
      maxPages: 1,
      delayBetweenRequests: 500,
      minSize: 20 // Lower threshold to see more apartments
    });
    const apartments = await scraper.scrapeApartments();
    
    console.log(`Found ${apartments.length} apartments`);
    
    // Check first few apartments
    console.log('\n📋 First 5 apartments location data:');
    apartments.slice(0, 5).forEach((apt, index) => {
      console.log(`\n${index + 1}. ${apt.title}`);
      console.log(`   Full Location: "${apt.fullLocation}"`);
      console.log(`   Location Parts: [${apt.locationParts.map(p => `"${p}"`).join(', ')}]`);
      console.log(`   Main Location: "${apt.location}"`);
      console.log(`   Price: €${apt.price}, Size: ${apt.size}m², Price/m²: €${apt.pricePerSqm}`);
      console.log(`   Floor: ${apt.floor}, Ground: ${apt.isGroundFloor}, Duplex: ${apt.isDuplex}`);
    });
    
    // Check location parsing
    console.log('\n🗺️  Location analysis:');
    const locationCounts = new Map<string, number>();
    
    apartments.forEach(apt => {
      apt.locationParts.forEach(part => {
        const cleanPart = part.trim();
        if (cleanPart && !cleanPart.toLowerCase().includes('beograd')) {
          locationCounts.set(cleanPart, (locationCounts.get(cleanPart) || 0) + 1);
        }
      });
    });
    
    console.log('Most common location parts:');
    Array.from(locationCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .forEach(([location, count]) => {
        console.log(`   "${location}": ${count} apartments`);
      });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

debugAnalyzer();
