import axios from 'axios';
import * as cheerio from 'cheerio';

async function debugElements() {
  console.log('🔍 Debugging HTML elements...');
  
  try {
    const url = 'https://www.halooglasi.com/nekretnine/prodaja-stanova/beograd';
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      timeout: 10000
    });

    const $ = cheerio.load(response.data);
    
    // Find apartment links
    const apartmentLinks = $('a[href*="/nekretnine/prodaja-stanova/"][href*="?kid="]');
    console.log(`Found ${apartmentLinks.length} apartment links`);
    
    // Check first apartment for HTML elements
    const $firstLink = apartmentLinks.first();
    const url_href = $firstLink.attr('href');
    console.log(`\nChecking first apartment: ${url_href}`);

    // Also check the individual apartment page
    if (url_href) {
      console.log('\n🔍 Checking individual apartment page...');
      const fullUrl = url_href.startsWith('http') ? url_href : `https://www.halooglasi.com${url_href}`;

      try {
        const detailResponse = await axios.get(fullUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          },
          timeout: 10000
        });

        const $detail = cheerio.load(detailResponse.data);

        console.log('Individual page plh elements:');
        const $plh2_detail = $detail('#plh2');
        const $plh3_detail = $detail('#plh3');
        const $plh4_detail = $detail('#plh4');
        const $plh18_detail = $detail('#plh18');

        console.log(`  plh2: "${$plh2_detail.text().trim()}"`);
        console.log(`  plh3: "${$plh3_detail.text().trim()}"`);
        console.log(`  plh4: "${$plh4_detail.text().trim()}"`);
        console.log(`  plh18: "${$plh18_detail.text().trim()}"`);

      } catch (error) {
        console.error('Error fetching individual page:', error);
      }
    }
    
    // Search for plh elements at different levels
    console.log('\n🔍 Searching for plh elements...');
    
    // Check in the entire page
    const $plh2_global = $('#plh2');
    const $plh3_global = $('#plh3');
    const $plh4_global = $('#plh4');
    const $plh18_global = $('#plh18');
    
    console.log(`Global search:`);
    console.log(`  plh2 (${$plh2_global.length}): "${$plh2_global.text().trim()}"`);
    console.log(`  plh3 (${$plh3_global.length}): "${$plh3_global.text().trim()}"`);
    console.log(`  plh4 (${$plh4_global.length}): "${$plh4_global.text().trim()}"`);
    console.log(`  plh18 (${$plh18_global.length}): "${$plh18_global.text().trim()}"`);
    
    // Check in different container levels around the first link
    let $container = $firstLink;
    for (let level = 0; level < 8; level++) {
      $container = $container.parent();
      if ($container.length === 0) break;
      
      const $plh2 = $container.find('#plh2');
      const $plh3 = $container.find('#plh3');
      const $plh4 = $container.find('#plh4');
      const $plh18 = $container.find('#plh18');
      
      if ($plh2.length > 0 || $plh3.length > 0 || $plh4.length > 0 || $plh18.length > 0) {
        console.log(`\nLevel ${level} container:`);
        console.log(`  plh2 (${$plh2.length}): "${$plh2.text().trim()}"`);
        console.log(`  plh3 (${$plh3.length}): "${$plh3.text().trim()}"`);
        console.log(`  plh4 (${$plh4.length}): "${$plh4.text().trim()}"`);
        console.log(`  plh18 (${$plh18.length}): "${$plh18.text().trim()}"`);
      }
    }
    
    // Also check for elements with these IDs anywhere in the page
    console.log('\n🔍 All elements with plh IDs:');
    $('[id^="plh"]').each((i, el) => {
      const $el = $(el);
      const id = $el.attr('id');
      const text = $el.text().trim();
      if (text.length > 0) {
        console.log(`  ${id}: "${text}"`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

debugElements();
