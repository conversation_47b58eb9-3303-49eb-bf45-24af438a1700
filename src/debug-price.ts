import axios from 'axios';
import * as cheerio from 'cheerio';

async function debugPrice() {
  console.log('🔍 Debugging price extraction...');
  
  try {
    const url = 'https://www.halooglasi.com/nekretnine/prodaja-stanova/beograd';
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      timeout: 10000
    });

    const $ = cheerio.load(response.data);
    
    // Find the specific apartment link
    const targetUrl = '/nekretnine/prodaja-stanova/beograd-na-vodi/5425645568603?kid=4';
    const $targetLink = $(`a[href="${targetUrl}"]`);
    
    if ($targetLink.length > 0) {
      console.log('✅ Found target apartment link');
      
      // Get different levels of parent containers and look for price elements
      let $container = $targetLink;
      for (let i = 0; i < 10; i++) {
        $container = $container.parent();
        const containerText = $container.text().replace(/\s+/g, ' ').trim();

        console.log(`\n📋 Parent level ${i + 1}:`);
        console.log('Text:', containerText.substring(0, 300));

        // Look for price patterns
        const priceMatches = containerText.match(/\d+[.,\d]*\s*€/g);
        if (priceMatches) {
          console.log('💰 Price matches found:', priceMatches);
        }

        // Also look for price elements within this container
        const $priceElements = $container.find('*').filter((_i, el) => {
          const text = $(el).text();
          return /\d+[.,\d]*\s*€/.test(text) && text.length < 100;
        });

        if ($priceElements.length > 0) {
          console.log('💰 Price elements found:');
          $priceElements.each((_j, priceEl) => {
            const priceText = $(priceEl).text().trim();
            console.log(`   "${priceText}"`);
          });
        }

        if ($container.is('body') || $container.length === 0) break;
      }

      
    } else {
      console.log('❌ Target apartment link not found');
      
      // Show all apartment links to see what's available
      const apartmentLinks = $('a[href*="/nekretnine/prodaja-stanova/"][href*="?kid="]');
      console.log(`\n📋 Found ${apartmentLinks.length} apartment links:`);
      
      apartmentLinks.slice(0, 5).each((index, element) => {
        const href = $(element).attr('href');
        const title = $(element).text().trim();
        console.log(`${index + 1}. ${title} - ${href}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

debugPrice();
