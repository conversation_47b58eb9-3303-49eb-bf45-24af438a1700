import { Apartment, LocationGroup, ApartmentAnalysis, GroupAnalysis } from '../types/apartment';

export class ApartmentAnalyzer {
  
  analyzeApartments(apartments: Apartment[]): GroupAnalysis {
    console.log(`📊 Analyzing ${apartments.length} apartments...`);
    
    // Group apartments by location hierarchy
    const groups = this.groupApartmentsByLocation(apartments);
    
    // Calculate statistics for each group
    this.calculateGroupStatistics(groups);
    
    // Analyze each apartment relative to its groups
    const apartmentAnalyses = this.analyzeApartmentValues(apartments, groups);
    
    // Find top undervalued apartments
    const topUndervaluedByGroup = this.findTopUndervaluedByGroup(apartmentAnalyses, groups);
    const overallTopUndervalued = this.findOverallTopUndervalued(apartmentAnalyses);
    
    return {
      groups,
      topUndervaluedByGroup,
      overallTopUndervalued
    };
  }

  private groupApartmentsByLocation(apartments: Apartment[]): Map<string, LocationGroup> {
    const groups = new Map<string, LocationGroup>();

    for (const apartment of apartments) {
      // Parse location parts from format: ["Opština Novi Beograd", "Blok 28", "Bulevar Milutina <PERSON>"]
      const locationParts = apartment.locationParts;

      if (locationParts.length === 0) continue;

      // Main group is the first part (usually municipality like "Opština Novi Beograd")
      const mainGroupName = locationParts[0];

      if (!groups.has(mainGroupName)) {
        groups.set(mainGroupName, {
          name: mainGroupName,
          apartments: [],
          averagePricePerSqm: 0,
          medianPricePerSqm: 0,
          apartmentCount: 0,
          subGroups: new Map()
        });
      }

      const mainGroup = groups.get(mainGroupName)!;
      mainGroup.apartments.push(apartment);

      // Subgroup is the second part if available (like "Blok 28")
      if (locationParts.length > 1) {
        const subGroupName = locationParts[1];

        if (!mainGroup.subGroups!.has(subGroupName)) {
          mainGroup.subGroups!.set(subGroupName, {
            name: subGroupName,
            apartments: [],
            averagePricePerSqm: 0,
            medianPricePerSqm: 0,
            apartmentCount: 0
          });
        }

        const subGroup = mainGroup.subGroups!.get(subGroupName)!;
        subGroup.apartments.push(apartment);
      }
    }

    return groups;
  }

  private calculateGroupStatistics(groups: Map<string, LocationGroup>): void {
    for (const [groupName, group] of groups) {
      // Calculate main group statistics
      this.calculateSingleGroupStatistics(group);
      
      // Calculate subgroup statistics
      if (group.subGroups) {
        for (const [subGroupName, subGroup] of group.subGroups) {
          this.calculateSingleGroupStatistics(subGroup);
        }
      }
    }
  }

  private calculateSingleGroupStatistics(group: LocationGroup): void {
    if (group.apartments.length === 0) return;
    
    const pricesPerSqm = group.apartments.map(apt => apt.pricePerSqm).sort((a, b) => a - b);
    
    // Calculate average
    group.averagePricePerSqm = Math.round(
      (pricesPerSqm.reduce((sum, price) => sum + price, 0) / pricesPerSqm.length) * 100
    ) / 100;
    
    // Calculate median
    const mid = Math.floor(pricesPerSqm.length / 2);
    group.medianPricePerSqm = pricesPerSqm.length % 2 === 0
      ? Math.round(((pricesPerSqm[mid - 1] + pricesPerSqm[mid]) / 2) * 100) / 100
      : pricesPerSqm[mid];
    
    group.apartmentCount = group.apartments.length;
    
    console.log(`📍 ${group.name}: ${group.apartmentCount} apartments, avg: €${group.averagePricePerSqm}/m², median: €${group.medianPricePerSqm}/m²`);
  }

  private analyzeApartmentValues(apartments: Apartment[], groups: Map<string, LocationGroup>): ApartmentAnalysis[] {
    const analyses: ApartmentAnalysis[] = [];
    
    for (const apartment of apartments) {
      const analysis = this.analyzeApartmentValue(apartment, groups);
      if (analysis) {
        analyses.push(analysis);
      }
    }
    
    return analyses;
  }

  private analyzeApartmentValue(apartment: Apartment, groups: Map<string, LocationGroup>): ApartmentAnalysis | null {
    const locationParts = apartment.locationParts;

    if (locationParts.length === 0) return null;

    const mainGroupName = locationParts[0];
    const mainGroup = groups.get(mainGroupName);

    if (!mainGroup) return null;

    // Calculate rating relative to main group
    const mainGroupRating = this.calculateRelativeRating(
      apartment.pricePerSqm,
      mainGroup.averagePricePerSqm
    );

    // Calculate rating relative to subgroup if available
    let subGroupRating = mainGroupRating;
    let subGroupName = mainGroupName;

    if (locationParts.length > 1) {
      const subGroupNameCandidate = locationParts[1];
      const subGroup = mainGroup.subGroups?.get(subGroupNameCandidate);

      if (subGroup && subGroup.apartmentCount >= 3) { // Only use subgroup if it has enough data
        subGroupRating = this.calculateRelativeRating(
          apartment.pricePerSqm,
          subGroup.averagePricePerSqm
        );
        subGroupName = subGroupNameCandidate;
      }
    }

    const isUndervalued = mainGroupRating < -5 || subGroupRating < -10; // More than 5% below main group or 10% below subgroup

    return {
      apartment,
      mainGroupRating,
      subGroupRating,
      mainGroupName,
      subGroupName,
      isUndervalued
    };
  }

  private calculateRelativeRating(apartmentPrice: number, groupAverage: number): number {
    // Returns percentage difference from group average
    // Negative values indicate undervalued (below average)
    // Positive values indicate overvalued (above average)
    return Math.round(((apartmentPrice - groupAverage) / groupAverage) * 10000) / 100;
  }

  private findTopUndervaluedByGroup(
    analyses: ApartmentAnalysis[], 
    groups: Map<string, LocationGroup>
  ): Map<string, ApartmentAnalysis[]> {
    const topByGroup = new Map<string, ApartmentAnalysis[]>();
    
    for (const [groupName, group] of groups) {
      const groupAnalyses = analyses.filter(analysis => 
        analysis.mainGroupName === groupName && analysis.isUndervalued
      );
      
      // Sort by subgroup rating (most undervalued first)
      const sortedAnalyses = groupAnalyses
        .sort((a, b) => a.subGroupRating - b.subGroupRating)
        .slice(0, 20); // Top 20 per group
      
      if (sortedAnalyses.length > 0) {
        topByGroup.set(groupName, sortedAnalyses);
      }
    }
    
    return topByGroup;
  }

  private findOverallTopUndervalued(analyses: ApartmentAnalysis[]): ApartmentAnalysis[] {
    return analyses
      .filter(analysis => analysis.isUndervalued)
      .sort((a, b) => {
        // Primary sort: subgroup rating (most undervalued first)
        const subGroupDiff = a.subGroupRating - b.subGroupRating;
        if (Math.abs(subGroupDiff) > 1) return subGroupDiff;

        // Secondary sort: main group rating
        return a.mainGroupRating - b.mainGroupRating;
      })
      .slice(0, 200); // Top 200 overall
  }
}
