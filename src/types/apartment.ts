export interface Apartment {
  id: string;
  title: string;
  price: number; // in EUR
  size: number; // in sqm
  pricePerSqm: number; // calculated EUR/sqm
  location: string;
  fullLocation: string; // full location string from the site
  locationParts: string[]; // parsed location parts
  floor: number | string;
  isGroundFloor: boolean;
  isDuplex: boolean;
  url: string;
  description?: string;
  imageUrl?: string;
  datePosted?: Date;
  heating?: string;
  furnished?: boolean;
  parking?: boolean;
  balcony?: boolean;
  elevator?: boolean;
  newBuilding?: boolean;
}

export interface LocationGroup {
  name: string;
  apartments: Apartment[];
  averagePricePerSqm: number;
  medianPricePerSqm: number;
  apartmentCount: number;
  subGroups?: Map<string, LocationGroup>;
}

export interface ApartmentAnalysis {
  apartment: Apartment;
  mainGroupRating: number; // relative to main group average (negative = undervalued)
  subGroupRating: number; // relative to subgroup average (negative = undervalued)
  mainGroupName: string;
  subGroupName: string;
  isUndervalued: boolean;
}

export interface GroupAnalysis {
  groups: Map<string, LocationGroup>;
  topUndervaluedByGroup: Map<string, ApartmentAnalysis[]>;
  overallTopUndervalued: ApartmentAnalysis[];
}

export interface ScrapingConfig {
  maxPages: number;
  delayBetweenRequests: number; // in milliseconds
  minSize: number; // minimum sqm
  excludeGroundFloor: boolean;
  excludeDuplex: boolean;
  belgradeOnly: boolean;
}

export interface FilterCriteria {
  minSize: number;
  excludeGroundFloor: boolean;
  excludeDuplex: boolean;
  minFloor: number;
}
