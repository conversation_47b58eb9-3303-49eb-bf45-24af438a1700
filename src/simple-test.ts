import { HaloOglasiScraper } from './scraper/halo-oglasi-scraper';

async function simpleTest() {
  console.log('🧪 Simple test...');
  
  try {
    const scraper = new HaloOglasiScraper({ maxPages: 3, delayBetweenRequests: 1000, minSize: 50 });
    const apartments = await scraper.scrapeApartments();
    
    console.log(`Found ${apartments.length} apartments`);
    
    if (apartments.length > 0) {
      const apt = apartments[0];
      console.log('First apartment:');
      console.log(`  Title: ${apt.title}`);
      console.log(`  Price: €${apt.price}`);
      console.log(`  Size: ${apt.size}m²`);
      console.log(`  Location: ${apt.fullLocation}`);
      console.log(`  Location Parts: [${apt.locationParts.map(p => `"${p}"`).join(', ')}]`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

simpleTest();
