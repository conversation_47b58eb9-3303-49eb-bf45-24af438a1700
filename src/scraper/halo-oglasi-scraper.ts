import axios from 'axios';
import * as cheerio from 'cheerio';
import { Apartment, ScrapingConfig, FilterCriteria } from '../types/apartment';

export class HaloOglasiScraper {
  private config: ScrapingConfig;
  private baseUrl = 'https://www.halooglasi.com';
  private searchUrl = '/nekretnine/prodaja-stanova/beograd';

  constructor(config?: Partial<ScrapingConfig>) {
    this.config = {
      maxPages: 10,
      delayBetweenRequests: 1000,
      minSize: 50,
      excludeGroundFloor: true,
      excludeDuplex: true,
      belgradeOnly: true,
      ...config
    };
  }

  async scrapeApartments(): Promise<Apartment[]> {
    const apartments: Apartment[] = [];
    
    for (let page = 1; page <= this.config.maxPages; page++) {
      console.log(`📄 Scraping page ${page}/${this.config.maxPages}...`);
      
      try {
        const pageApartments = await this.scrapePage(page);
        const filteredApartments = this.filterApartments(pageApartments);
        apartments.push(...filteredApartments);

        console.log(`   Found ${pageApartments.length} apartments, ${filteredApartments.length} after filtering`);

        // If no apartments found on this page, likely reached the end
        if (pageApartments.length === 0) {
          console.log(`   No more apartments found, stopping at page ${page}`);
          break;
        }

        // Delay between requests to be respectful
        if (page < this.config.maxPages) {
          await this.delay(this.config.delayBetweenRequests);
        }
      } catch (error) {
        console.error(`❌ Error scraping page ${page}:`, error);
        continue;
      }
    }

    return apartments;
  }

  private async scrapePage(page: number): Promise<Apartment[]> {
    const url = `${this.baseUrl}${this.searchUrl}?page=${page}`;
    
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      const apartments: Apartment[] = [];

      // Find apartment listings based on actual Halo Oglasi structure
      // Look for links to individual apartments (contain ?kid= parameter)
      const apartmentLinks = $('a[href*="/nekretnine/prodaja-stanova/"][href*="?kid="]');

      apartmentLinks.each((index, element) => {
        try {
          const apartment = this.parseApartmentElement($, element);
          if (apartment) {
            apartments.push(apartment);
          } else {
            console.log(`   Apartment ${index + 1} failed parsing`);
          }
        } catch (error) {
          console.warn(`⚠️  Error parsing apartment ${index}:`, error);
        }
      });

      return apartments;
    } catch (error) {
      console.error(`❌ Error fetching page ${page}:`, error);
      throw error;
    }
  }

  private parseApartmentElement($: cheerio.CheerioAPI, element: any): Apartment | null {
    const $link = $(element);
    const url = $link.attr('href');

    console.error(`DEBUG: Starting parse for URL: ${url}`);

    if (!url) {
      console.error(`DEBUG: No URL found`);
      return null;
    }

    // Get the parent container that has all the apartment info
    // Try multiple levels of parent containers to find price info
    let $container = $link;
    let containerText = '';

    // Go up parent levels until we find a container with price info
    for (let level = 0; level < 5; level++) {
      $container = $container.parent();
      containerText = $container.text().replace(/\s+/g, ' ').trim();

      console.error(`DEBUG: Level ${level}, container text: "${containerText.substring(0, 100)}..."`);

      // Check if this level contains price info
      if (containerText.match(/\d+[.,]\d+\s*€/)) {
        console.error(`DEBUG: Found price at level ${level}`);
        break;
      }
    }

    const title = $link.text().trim() || $container.find('h3, h2').text().trim();

    // Extract price (format: "1.045.000 €" or "950.000 €")
    // Look for the full price pattern including multiple dots/commas
    const allPriceMatches = containerText.match(/\d+(?:[.,]\d+)*\s*€/g);

    // Take the first (main) price, not the per-sqm price
    const priceMatch = allPriceMatches && allPriceMatches.length > 0 ? allPriceMatches[0] : '';
    const priceText = priceMatch;

    // Extract location - look for "Beograd" followed by location info
    const locationMatch = containerText.match(/Beograd[^€\d]+/);
    const locationText = locationMatch ? locationMatch[0].replace(/\s+/g, ' ').trim() : '';

    // Extract size (format: "175 m2")
    const sizeMatch = containerText.match(/(\d+(?:[.,]\d+)?)\s*m2/i);
    const sizeText = sizeMatch ? sizeMatch[0] : '';

    console.error(`DEBUG: Extracted - title="${title}", price="${priceText}", location="${locationText}", size="${sizeText}"`);
    console.error(`DEBUG: URL="${url}"`);
    console.error(`DEBUG: All price matches: ${allPriceMatches}`);
    console.error(`DEBUG: Location match: ${locationMatch ? locationMatch[0] : 'none'}`);
    console.error(`DEBUG: Size match: ${sizeMatch ? sizeMatch[0] : 'none'}`);

    if (!title || !priceText || !locationText || !url) {
      console.error(`   MISSING DATA: title="${title}", price="${priceText}", location="${locationText}", url="${url}"`);
      return null;
    }

    console.error(`DEBUG: Validation passed, continuing with parsing...`);

    // Parse price (remove EUR, €, and other currency symbols)
    const price = this.parsePrice(priceText);
    console.error(`DEBUG: Parsed price: ${price} from "${priceText}"`);
    if (!price || price <= 0) {
      console.error(`DEBUG: Price validation failed: ${price}`);
      return null;
    }

    // Parse size
    const size = this.parseSize(sizeText);
    console.error(`DEBUG: Parsed size: ${size} from "${sizeText}"`);
    if (!size || size < this.config.minSize) {
      console.error(`DEBUG: Size validation failed: ${size} < ${this.config.minSize}`);
      return null;
    }

    // Parse location
    const locationParts = this.parseLocation(locationText);
    console.error(`DEBUG: Location parts: [${locationParts.map(p => `"${p}"`).join(', ')}]`);

    const isValidLocation = this.isValidBelgradeLocation(locationParts);
    console.error(`DEBUG: Belgrade validation: ${isValidLocation}`);

    if (!isValidLocation) {
      console.error(`DEBUG: Location validation failed for: ${locationText}`);
      return null;
    }

    // Parse floor info
    const floorInfo = this.parseFloorInfo(containerText);
    
    const apartment: Apartment = {
      id: this.generateId(url),
      title,
      price,
      size,
      pricePerSqm: Math.round((price / size) * 100) / 100,
      location: locationParts[locationParts.length - 1] || locationText,
      fullLocation: locationText,
      locationParts,
      floor: floorInfo.floor,
      isGroundFloor: floorInfo.isGroundFloor,
      isDuplex: floorInfo.isDuplex,
      url: url.startsWith('http') ? url : `${this.baseUrl}${url}`
    };

    return apartment;
  }

  private parsePrice(priceText: string): number {
    // Handle formats like "950.000 €" or "950,000 €" or "950 000 €"
    // Remove currency symbols and spaces, keep digits and separators
    let cleanPrice = priceText.replace(/[€$\s]/g, '');

    // Handle European format (950.000,50) vs US format (950,000.50)
    // If there are multiple dots/commas, assume European format
    const dotCount = (cleanPrice.match(/\./g) || []).length;
    const commaCount = (cleanPrice.match(/,/g) || []).length;

    if (dotCount > 1 || (dotCount === 1 && commaCount === 1 && cleanPrice.indexOf('.') < cleanPrice.indexOf(','))) {
      // European format: 950.000,50 -> 950000.50
      cleanPrice = cleanPrice.replace(/\./g, '').replace(',', '.');
    } else if (commaCount > 1 || (dotCount === 1 && commaCount === 1 && cleanPrice.indexOf(',') < cleanPrice.indexOf('.'))) {
      // US format: 950,000.50 -> 950000.50
      cleanPrice = cleanPrice.replace(/,/g, '');
    } else if (dotCount === 0 && commaCount === 1) {
      // Single comma, could be decimal: 950,50 -> 950.50
      cleanPrice = cleanPrice.replace(',', '.');
    } else if (dotCount === 1 && commaCount === 0) {
      // Single dot, could be thousands separator or decimal
      // If more than 3 digits after dot, it's thousands separator
      const parts = cleanPrice.split('.');
      if (parts[1] && parts[1].length > 2) {
        cleanPrice = cleanPrice.replace('.', '');
      }
    }

    const price = parseFloat(cleanPrice);
    return isNaN(price) ? 0 : price;
  }

  private parseSize(sizeText: string): number {
    const match = sizeText.match(/(\d+(?:[.,]\d+)?)\s*m²?/i);
    if (match) {
      return parseFloat(match[1].replace(',', '.'));
    }
    return 0;
  }

  private parseLocation(locationText: string): string[] {
    // Parse format: "Beograd Opština Savski venac Dedinje Miloja Đaka"
    // Extract: ["Opština Savski venac", "Dedinje", "Miloja Đaka"]

    const parts: string[] = [];

    // Remove "Beograd" from the beginning
    let cleanLocation = locationText.replace(/^Beograd\s+/, '').trim();

    if (cleanLocation.includes('Opština')) {
      // Find "Opština" and extract municipality
      const words = cleanLocation.split(/\s+/);
      const opshtinaIndex = words.findIndex(word => word === 'Opština');

      if (opshtinaIndex >= 0 && opshtinaIndex + 1 < words.length) {
        // Municipality: "Opština [Name]" (could be multiple words)
        let municipalityEnd = opshtinaIndex + 2; // Start with "Opština Name"

        // Check if next word is part of municipality name (like "Savski venac")
        if (municipalityEnd < words.length &&
            (words[municipalityEnd].toLowerCase() === 'venac' ||
             words[municipalityEnd].toLowerCase() === 'beograd' ||
             words[municipalityEnd].toLowerCase() === 'grad')) {
          municipalityEnd++;
        }

        const municipality = words.slice(opshtinaIndex, municipalityEnd).join(' ');
        parts.push(municipality);

        // Remaining parts are area and street
        const remaining = words.slice(municipalityEnd);
        if (remaining.length >= 1) {
          parts.push(remaining[0]); // Area (like "Dedinje")
        }
        if (remaining.length >= 2) {
          parts.push(remaining.slice(1).join(' ')); // Street
        }
      }
    } else {
      // Fallback: split by spaces and take first few parts
      const words = cleanLocation.split(/\s+/);
      if (words.length >= 1) parts.push(words[0]);
      if (words.length >= 2) parts.push(words.slice(1).join(' '));
    }

    return parts.filter(part => part.length > 0);
  }

  private isValidBelgradeLocation(locationParts: string[]): boolean {
    if (!this.config.belgradeOnly) return true;
    
    const locationStr = locationParts.join(' ').toLowerCase();
    return locationStr.includes('beograd') || locationStr.includes('belgrade');
  }

  private parseFloorInfo(containerText: string): {
    floor: number | string;
    isGroundFloor: boolean;
    isDuplex: boolean;
  } {
    const allText = containerText.toLowerCase();
    
    const isGroundFloor = /prizemlje|ground|0\s*sprat/i.test(allText);
    const isDuplex = /duplex|dupleks|maisonette/i.test(allText);
    
    // Try to extract floor number
    const floorMatch = allText.match(/(\d+)\.?\s*sprat/);
    let floor: number | string = 'unknown';
    
    if (isGroundFloor) {
      floor = 0;
    } else if (floorMatch) {
      floor = parseInt(floorMatch[1]);
    }
    
    return { floor, isGroundFloor, isDuplex };
  }

  private filterApartments(apartments: Apartment[]): Apartment[] {
    const criteria: FilterCriteria = {
      minSize: this.config.minSize,
      excludeGroundFloor: this.config.excludeGroundFloor,
      excludeDuplex: this.config.excludeDuplex,
      minFloor: 1
    };

    return apartments.filter(apt => {
      if (apt.size < criteria.minSize) return false;
      if (criteria.excludeGroundFloor && apt.isGroundFloor) return false;
      if (criteria.excludeDuplex && apt.isDuplex) return false;
      return true;
    });
  }

  private generateId(url: string): string {
    // Extract ID from URL or generate from URL hash
    const match = url.match(/\/(\d+)(?:\/|$)/);
    return match ? match[1] : Buffer.from(url).toString('base64').slice(0, 10);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
