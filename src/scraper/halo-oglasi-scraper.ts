import axios from 'axios';
import * as cheerio from 'cheerio';
import { Apartment, ScrapingConfig, FilterCriteria } from '../types/apartment';

export class HaloOglasiScraper {
  private config: ScrapingConfig;
  private baseUrl = 'https://www.halooglasi.com';
  private searchUrl = '/nekretnine/prodaja-stanova/beograd';

  constructor(config?: Partial<ScrapingConfig>) {
    this.config = {
      maxPages: 10,
      delayBetweenRequests: 1000,
      minSize: 50,
      excludeGroundFloor: true,
      excludeDuplex: true,
      belgradeOnly: true,
      ...config
    };
  }

  async scrapeApartments(): Promise<Apartment[]> {
    const apartments: Apartment[] = [];
    
    for (let page = 1; page <= this.config.maxPages; page++) {
      console.log(`📄 Scraping page ${page}/${this.config.maxPages}...`);
      
      try {
        const pageApartments = await this.scrapePage(page);
        const filteredApartments = this.filterApartments(pageApartments);
        apartments.push(...filteredApartments);
        
        console.log(`   Found ${pageApartments.length} apartments, ${filteredApartments.length} after filtering`);
        
        // Delay between requests to be respectful
        if (page < this.config.maxPages) {
          await this.delay(this.config.delayBetweenRequests);
        }
      } catch (error) {
        console.error(`❌ Error scraping page ${page}:`, error);
        continue;
      }
    }

    return apartments;
  }

  private async scrapePage(page: number): Promise<Apartment[]> {
    const url = `${this.baseUrl}${this.searchUrl}?page=${page}`;
    
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      const apartments: Apartment[] = [];

      // Find apartment listings based on actual Halo Oglasi structure
      // Look for links to individual apartments (contain ?kid= parameter)
      const apartmentLinks = $('a[href*="/nekretnine/prodaja-stanova/"][href*="?kid="]');

      apartmentLinks.each((index, element) => {
        try {
          const apartment = this.parseApartmentElement($, element);
          if (apartment) {
            apartments.push(apartment);
          }
        } catch (error) {
          console.warn(`⚠️  Error parsing apartment ${index}:`, error);
        }
      });

      return apartments;
    } catch (error) {
      console.error(`❌ Error fetching page ${page}:`, error);
      throw error;
    }
  }

  private parseApartmentElement($: cheerio.CheerioAPI, element: any): Apartment | null {
    const $link = $(element);
    const url = $link.attr('href');

    if (!url) return null;

    // Get the parent container that has all the apartment info
    // Try multiple levels of parent containers to find price info
    let $container = $link.closest('div, article, li');
    let containerText = $container.text().replace(/\s+/g, ' ').trim();

    // If no price found, try going up one more level
    if (!containerText.match(/\d+[.,]\d+\s*€/)) {
      $container = $container.parent();
      containerText = $container.text().replace(/\s+/g, ' ').trim();
    }

    // If still no price, try the whole page context around this link
    if (!containerText.match(/\d+[.,]\d+\s*€/)) {
      // Look for price in nearby elements
      const $priceElement = $link.closest('*').find('*').filter((_i, el) => {
        const text = $(el).text();
        return /\d+[.,]\d+\s*€/.test(text) && text.length < 50;
      }).first();

      if ($priceElement.length > 0) {
        const priceText = $priceElement.text().match(/(\d+[.,]\d+)\s*€/)?.[0] || '';
        containerText = `${containerText} ${priceText}`;
      }
    }

    const title = $link.text().trim() || $container.find('h3, h2').text().trim();

    // Extract price (format: "950.000 €" or "950,000 €")
    const priceMatch = containerText.match(/(\d+[.,]\d+)\s*€/);
    const priceText = priceMatch ? priceMatch[0] : '';

    // Extract location - look for "Beograd" followed by location info
    const locationMatch = containerText.match(/Beograd[^€\d]+/);
    const locationText = locationMatch ? locationMatch[0].replace(/\s+/g, ' ').trim() : '';

    // Extract size (format: "175 m2")
    const sizeMatch = containerText.match(/(\d+(?:[.,]\d+)?)\s*m2/i);
    const sizeText = sizeMatch ? sizeMatch[0] : '';

    if (!title || !priceText || !locationText || !url) {
      return null;
    }

    // Parse price (remove EUR, €, and other currency symbols)
    const price = this.parsePrice(priceText);
    if (!price || price <= 0) return null;

    // Parse size
    const size = this.parseSize(sizeText);
    if (!size || size < this.config.minSize) return null;

    // Parse location
    const locationParts = this.parseLocation(locationText);
    if (!this.isValidBelgradeLocation(locationParts)) return null;

    // Parse floor info
    const floorInfo = this.parseFloorInfo(containerText);
    
    const apartment: Apartment = {
      id: this.generateId(url),
      title,
      price,
      size,
      pricePerSqm: Math.round((price / size) * 100) / 100,
      location: locationParts[locationParts.length - 1] || locationText,
      fullLocation: locationText,
      locationParts,
      floor: floorInfo.floor,
      isGroundFloor: floorInfo.isGroundFloor,
      isDuplex: floorInfo.isDuplex,
      url: url.startsWith('http') ? url : `${this.baseUrl}${url}`
    };

    return apartment;
  }

  private parsePrice(priceText: string): number {
    // Handle formats like "950.000 €" or "950,000 €" or "950 000 €"
    // Remove currency symbols and spaces, keep digits and separators
    let cleanPrice = priceText.replace(/[€$\s]/g, '');

    // Handle European format (950.000,50) vs US format (950,000.50)
    // If there are multiple dots/commas, assume European format
    const dotCount = (cleanPrice.match(/\./g) || []).length;
    const commaCount = (cleanPrice.match(/,/g) || []).length;

    if (dotCount > 1 || (dotCount === 1 && commaCount === 1 && cleanPrice.indexOf('.') < cleanPrice.indexOf(','))) {
      // European format: 950.000,50 -> 950000.50
      cleanPrice = cleanPrice.replace(/\./g, '').replace(',', '.');
    } else if (commaCount > 1 || (dotCount === 1 && commaCount === 1 && cleanPrice.indexOf(',') < cleanPrice.indexOf('.'))) {
      // US format: 950,000.50 -> 950000.50
      cleanPrice = cleanPrice.replace(/,/g, '');
    } else if (dotCount === 0 && commaCount === 1) {
      // Single comma, could be decimal: 950,50 -> 950.50
      cleanPrice = cleanPrice.replace(',', '.');
    } else if (dotCount === 1 && commaCount === 0) {
      // Single dot, could be thousands separator or decimal
      // If more than 3 digits after dot, it's thousands separator
      const parts = cleanPrice.split('.');
      if (parts[1] && parts[1].length > 2) {
        cleanPrice = cleanPrice.replace('.', '');
      }
    }

    const price = parseFloat(cleanPrice);
    return isNaN(price) ? 0 : price;
  }

  private parseSize(sizeText: string): number {
    const match = sizeText.match(/(\d+(?:[.,]\d+)?)\s*m²?/i);
    if (match) {
      return parseFloat(match[1].replace(',', '.'));
    }
    return 0;
  }

  private parseLocation(locationText: string): string[] {
    // Simple approach: split by spaces and take meaningful parts
    const words = locationText.split(/\s+/).filter(word => word.length > 0);
    const parts: string[] = [];

    // Look for "Opština" pattern
    for (let i = 0; i < words.length; i++) {
      if (words[i] === 'Opština' && i + 1 < words.length) {
        // Add municipality
        const municipality = `Opština ${words[i + 1]}`;
        parts.push(municipality);

        // Add next parts as area and street
        if (i + 2 < words.length) {
          parts.push(words[i + 2]); // Area
        }
        if (i + 3 < words.length) {
          parts.push(words.slice(i + 3).join(' ')); // Street
        }
        break;
      }
    }

    // If no "Opština" found, use first few non-"Beograd" words
    if (parts.length === 0) {
      const nonBelgradeWords = words.filter(word =>
        !word.toLowerCase().includes('beograd') &&
        !word.toLowerCase().includes('belgrade')
      );

      if (nonBelgradeWords.length >= 1) {
        parts.push(nonBelgradeWords[0]);
      }
      if (nonBelgradeWords.length >= 2) {
        parts.push(nonBelgradeWords.slice(1).join(' '));
      }
    }

    return parts.filter(part => part.length > 0);
  }

  private isValidBelgradeLocation(locationParts: string[]): boolean {
    if (!this.config.belgradeOnly) return true;
    
    const locationStr = locationParts.join(' ').toLowerCase();
    return locationStr.includes('beograd') || locationStr.includes('belgrade');
  }

  private parseFloorInfo(containerText: string): {
    floor: number | string;
    isGroundFloor: boolean;
    isDuplex: boolean;
  } {
    const allText = containerText.toLowerCase();
    
    const isGroundFloor = /prizemlje|ground|0\s*sprat/i.test(allText);
    const isDuplex = /duplex|dupleks|maisonette/i.test(allText);
    
    // Try to extract floor number
    const floorMatch = allText.match(/(\d+)\.?\s*sprat/);
    let floor: number | string = 'unknown';
    
    if (isGroundFloor) {
      floor = 0;
    } else if (floorMatch) {
      floor = parseInt(floorMatch[1]);
    }
    
    return { floor, isGroundFloor, isDuplex };
  }

  private filterApartments(apartments: Apartment[]): Apartment[] {
    const criteria: FilterCriteria = {
      minSize: this.config.minSize,
      excludeGroundFloor: this.config.excludeGroundFloor,
      excludeDuplex: this.config.excludeDuplex,
      minFloor: 1
    };

    return apartments.filter(apt => {
      if (apt.size < criteria.minSize) return false;
      if (criteria.excludeGroundFloor && apt.isGroundFloor) return false;
      if (criteria.excludeDuplex && apt.isDuplex) return false;
      return true;
    });
  }

  private generateId(url: string): string {
    // Extract ID from URL or generate from URL hash
    const match = url.match(/\/(\d+)(?:\/|$)/);
    return match ? match[1] : Buffer.from(url).toString('base64').slice(0, 10);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
