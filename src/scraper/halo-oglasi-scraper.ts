import axios from 'axios';
import * as cheerio from 'cheerio';
import { Apartment, ScrapingConfig, FilterCriteria } from '../types/apartment';

export class HaloOglasiScraper {
  private config: ScrapingConfig;
  private baseUrl = 'https://www.halooglasi.com';
  private searchUrl = '/nekretnine/prodaja-stanova/beograd';

  constructor(config?: Partial<ScrapingConfig>) {
    this.config = {
      maxPages: 10,
      delayBetweenRequests: 1000,
      minSize: 50,
      excludeGroundFloor: true,
      excludeDuplex: true,
      belgradeOnly: true,
      ...config
    };
  }

  async scrapeApartments(): Promise<Apartment[]> {
    const apartments: Apartment[] = [];
    
    for (let page = 1; page <= this.config.maxPages; page++) {
      console.log(`📄 Scraping page ${page}/${this.config.maxPages}...`);
      
      try {
        const pageApartments = await this.scrapePage(page);
        const filteredApartments = this.filterApartments(pageApartments);
        apartments.push(...filteredApartments);

        console.log(`   Found ${pageApartments.length} apartments, ${filteredApartments.length} after filtering`);

        // If no apartments found on this page, likely reached the end
        if (pageApartments.length === 0) {
          console.log(`   No more apartments found, stopping at page ${page}`);
          break;
        }

        // Delay between requests to be respectful
        if (page < this.config.maxPages) {
          await this.delay(this.config.delayBetweenRequests);
        }
      } catch (error) {
        console.error(`❌ Error scraping page ${page}:`, error);
        continue;
      }
    }

    return apartments;
  }

  private async scrapePage(page: number): Promise<Apartment[]> {
    const url = `${this.baseUrl}${this.searchUrl}?page=${page}`;
    
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      const apartments: Apartment[] = [];

      // Find apartment listings based on actual Halo Oglasi structure
      // Look for links to individual apartments (contain ?kid= parameter)
      const apartmentLinks = $('a[href*="/nekretnine/prodaja-stanova/"][href*="?kid="]');

      apartmentLinks.each((index, element) => {
        try {
          const apartment = this.parseApartmentElement($, element);
          if (apartment) {
            apartments.push(apartment);
          }
        } catch (error) {
          console.warn(`⚠️  Error parsing apartment ${index + 1}:`, error);
        }
      });

      return apartments;
    } catch (error) {
      console.error(`❌ Error fetching page ${page}:`, error);
      throw error;
    }
  }

  private parseApartmentElement($: cheerio.CheerioAPI, element: any): Apartment | null {
    const $link = $(element);
    const url = $link.attr('href');

    if (!url) return null;

    // Get the parent container that has all the apartment info
    // Try multiple levels of parent containers to find price info
    let $container = $link;
    let containerText = '';
    let foundPriceLevel = false;

    // Go up parent levels until we find a container with price info
    for (let level = 0; level < 8; level++) {
      $container = $container.parent();
      if ($container.length === 0) break;

      containerText = $container.text().replace(/\s+/g, ' ').trim();

      // Check if this level contains price info or location info
      if (containerText.match(/\d+[.,]\d+\s*€/) || containerText.includes('Beograd')) {
        foundPriceLevel = true;
        break;
      }
    }

    // If no suitable container found, skip this apartment
    if (!foundPriceLevel || containerText.length < 50) {
      return null;
    }

    // Extract title with multiple fallbacks
    let title = $link.text().trim();
    if (!title) {
      title = $container.find('h3, h2, h4, .title, .product-title').first().text().trim();
    }
    if (!title) {
      // Extract from URL as last resort
      const urlParts = url.split('/');
      title = urlParts[urlParts.length - 2] || 'Unknown';
      title = title.replace(/-/g, ' ').replace(/\d+/g, '').trim();
    }

    // Extract price using the offer-price-value class
    const $priceElement = $container.find('.offer-price-value');
    let priceText = '';

    if ($priceElement.length > 0) {
      priceText = $priceElement.text().trim();
    } else {
      // Fallback to text parsing
      const allPriceMatches = containerText.match(/\d+(?:[.,]\d+)*\s*€/g);
      priceText = allPriceMatches && allPriceMatches.length > 0 ? allPriceMatches[0] : '';
    }

    // Extract location using specific HTML elements inside product-details-desc
    const $detailsDesc = $container.find('.product-details-desc');
    const $plh2 = $detailsDesc.find('#plh2'); // City (Beograd)
    const $plh3 = $detailsDesc.find('#plh3'); // Main group
    const $plh4 = $detailsDesc.find('#plh4'); // Subgroup
    const $plh18 = $detailsDesc.find('#plh18'); // Floor (sprat)

    const city = $plh2.text().trim();
    const mainGroup = $plh3.text().trim();
    const subGroup = $plh4.text().trim();
    const floorFromPlh18 = $plh18.text().trim();

    // Fallback to text parsing if elements not found
    let locationText = '';
    if (city || mainGroup || subGroup) {
      locationText = [city, mainGroup, subGroup].filter(g => g.length > 0).join(' - ');
    } else {
      const locationMatch = containerText.match(/Beograd[^€\d]+/);
      locationText = locationMatch ? locationMatch[0].replace(/\s+/g, ' ').trim() : '';
    }

    // Extract size with multiple patterns
    let sizeText = '';
    const sizePatterns = [
      /(\d+(?:[.,]\d+)?)\s*m2/i,
      /(\d+(?:[.,]\d+)?)\s*m²/i,
      /(\d+(?:[.,]\d+)?)\s*квм/i, // Cyrillic
      /(\d+(?:[.,]\d+)?)\s*sqm/i,
      /Kvadratura\s*(\d+(?:[.,]\d+)?)/i
    ];

    for (const pattern of sizePatterns) {
      const match = containerText.match(pattern);
      if (match) {
        sizeText = match[0];
        break;
      }
    }



    // More lenient validation - only require essential fields
    if (!url) return null;
    if (!title || title.length < 3) return null;
    if (!priceText) return null;

    // Location is optional - some apartments might not have clear location data
    // We'll handle this in the analyzer

    // Parse price (remove EUR, €, and other currency symbols)
    const price = this.parsePrice(priceText);
    if (!price || price <= 0) return null;

    // Parse size
    const size = this.parseSize(sizeText);
    if (!size || size < this.config.minSize) return null;

    // Parse location - prioritize plh3/plh4 elements over text parsing
    let locationParts: string[] = [];

    if (mainGroup || subGroup) {
      // Use the structured data from HTML elements (this is more accurate)
      if (mainGroup && mainGroup.length > 0) {
        locationParts.push(mainGroup);
      }
      if (subGroup && subGroup.length > 0) {
        locationParts.push(subGroup);
      }

      console.log(`DEBUG: Using HTML elements - Main: "${mainGroup}", Sub: "${subGroup}"`);
    } else {
      // Fallback to text parsing only if HTML elements are not available
      locationParts = this.parseLocation(locationText);
      console.log(`DEBUG: Using text parsing - Parts: [${locationParts.map(p => `"${p}"`).join(', ')}]`);
    }

    // Only validate location if we have location data
    if (locationText && locationParts.length > 0) {
      const isValidLocation = this.isValidBelgradeLocation(locationParts, city);
      if (!isValidLocation) return null;
    }

    // Parse floor info
    const floorInfo = this.parseFloorInfo(containerText, floorFromPlh18);

    // Extract description for additional filtering
    const description = containerText; // Use container text as description for now

    // Check for exclusion criteria
    if (this.shouldExcludeApartment(floorInfo, description)) {
      return null;
    }

    const apartment: Apartment = {
      id: this.generateId(url),
      title,
      price,
      size,
      pricePerSqm: Math.round((price / size) * 100) / 100,
      location: locationParts[locationParts.length - 1] || locationText,
      fullLocation: locationText,
      locationParts,
      floor: floorInfo.floor,
      isGroundFloor: floorInfo.isGroundFloor,
      isDuplex: floorInfo.isDuplex,
      url: url.startsWith('http') ? url : `${this.baseUrl}${url}`,
      description: description.substring(0, 500) // Store first 500 chars of description
    };

    return apartment;
  }

  private parsePrice(priceText: string): number {
    // Handle formats from offer-price-value class like "179.000" or fallback formats like "950.000 €"
    // Remove currency symbols and spaces, keep digits and separators
    let cleanPrice = priceText.replace(/[€$\s]/g, '');

    // If the price is from offer-price-value class, it's typically in format "179.000"
    // where dots are thousands separators
    if (!priceText.includes('€') && cleanPrice.match(/^\d+\.\d{3}$/)) {
      // Format like "179.000" - remove dots (thousands separators)
      cleanPrice = cleanPrice.replace(/\./g, '');
    } else {
      // Handle other formats like "950.000 €" or "950,000 €" or "950 000 €"
      const dotCount = (cleanPrice.match(/\./g) || []).length;
      const commaCount = (cleanPrice.match(/,/g) || []).length;

      if (dotCount > 1 || (dotCount === 1 && commaCount === 1 && cleanPrice.indexOf('.') < cleanPrice.indexOf(','))) {
        // European format: 950.000,50 -> 950000.50
        cleanPrice = cleanPrice.replace(/\./g, '').replace(',', '.');
      } else if (commaCount > 1 || (dotCount === 1 && commaCount === 1 && cleanPrice.indexOf(',') < cleanPrice.indexOf('.'))) {
        // US format: 950,000.50 -> 950000.50
        cleanPrice = cleanPrice.replace(/,/g, '');
      } else if (dotCount === 0 && commaCount === 1) {
        // Single comma, could be decimal: 950,50 -> 950.50
        cleanPrice = cleanPrice.replace(',', '.');
      } else if (dotCount === 1 && commaCount === 0) {
        // Single dot, could be thousands separator or decimal
        // If more than 3 digits after dot, it's thousands separator
        const parts = cleanPrice.split('.');
        if (parts[1] && parts[1].length > 2) {
          cleanPrice = cleanPrice.replace('.', '');
        }
      }
    }

    const price = parseFloat(cleanPrice);
    return isNaN(price) ? 0 : price;
  }

  private parseSize(sizeText: string): number {
    if (!sizeText) return 0;

    // Try multiple patterns to extract size
    const patterns = [
      /(\d+(?:[.,]\d+)?)\s*m²?/i,
      /(\d+(?:[.,]\d+)?)\s*квм/i, // Cyrillic
      /(\d+(?:[.,]\d+)?)\s*sqm/i,
      /(\d+(?:[.,]\d+)?)/i // Just numbers as fallback
    ];

    for (const pattern of patterns) {
      const match = sizeText.match(pattern);
      if (match) {
        const size = parseFloat(match[1].replace(',', '.'));
        if (size > 0 && size < 1000) { // Reasonable size range
          return size;
        }
      }
    }

    return 0;
  }

  private parseLocation(locationText: string): string[] {
    // Parse format: "Beograd Opština Savski venac Dedinje Miloja Đaka"
    // Extract: ["Opština Savski venac", "Dedinje", "Miloja Đaka"]

    const parts: string[] = [];

    // Remove "Beograd" from the beginning
    let cleanLocation = locationText.replace(/^Beograd\s+/, '').trim();

    if (cleanLocation.includes('Opština')) {
      // Find "Opština" and extract municipality
      const words = cleanLocation.split(/\s+/);
      const opshtinaIndex = words.findIndex(word => word === 'Opština');

      if (opshtinaIndex >= 0 && opshtinaIndex + 1 < words.length) {
        // Municipality: "Opština [Name]" (could be multiple words)
        let municipalityEnd = opshtinaIndex + 2; // Start with "Opština Name"

        // Check if next word is part of municipality name (like "Savski venac")
        if (municipalityEnd < words.length &&
            (words[municipalityEnd].toLowerCase() === 'venac' ||
             words[municipalityEnd].toLowerCase() === 'beograd' ||
             words[municipalityEnd].toLowerCase() === 'grad')) {
          municipalityEnd++;
        }

        const municipality = words.slice(opshtinaIndex, municipalityEnd).join(' ');
        parts.push(municipality);

        // Remaining parts are area and street
        const remaining = words.slice(municipalityEnd);
        if (remaining.length >= 1) {
          parts.push(remaining[0]); // Area (like "Dedinje")
        }
        if (remaining.length >= 2) {
          parts.push(remaining.slice(1).join(' ')); // Street
        }
      }
    } else {
      // Fallback: split by spaces and take first few parts
      const words = cleanLocation.split(/\s+/);
      if (words.length >= 1) parts.push(words[0]);
      if (words.length >= 2) parts.push(words.slice(1).join(' '));
    }

    return parts.filter(part => part.length > 0);
  }

  private isValidBelgradeLocation(locationParts: string[], city?: string): boolean {
    if (!this.config.belgradeOnly) return true;

    // Check if city is explicitly "Beograd"
    if (city && (city.toLowerCase().includes('beograd') || city.toLowerCase().includes('belgrade'))) {
      return true;
    }

    // If we have location parts with "Opština", it's likely Belgrade
    const locationStr = locationParts.join(' ').toLowerCase();
    return locationStr.includes('beograd') ||
           locationStr.includes('belgrade') ||
           locationStr.includes('opština') ||
           locationParts.length > 0; // If we have any location parts, assume it's Belgrade for now
  }

  private parseFloorInfo(containerText: string, floorFromPlh18?: string): {
    floor: number | string;
    isGroundFloor: boolean;
    isDuplex: boolean;
    isPSUT: boolean;
    isExcludedFloor: boolean;
  } {
    const allText = containerText.toLowerCase();

    // Check plh18 field first for floor information
    let floor: number | string = 'unknown';
    let isGroundFloor = false;
    let isDuplex = false;
    let isPSUT = false;
    let isExcludedFloor = false;

    if (floorFromPlh18) {
      const floorText = floorFromPlh18.toUpperCase().trim();

      // Check for excluded floor types
      if (['PR', 'VPR', 'PSUT', 'SUT'].includes(floorText)) {
        isExcludedFloor = true;
        floor = floorText;

        if (floorText === 'PR') {
          isGroundFloor = true;
        } else if (['PSUT', 'SUT'].includes(floorText)) {
          isPSUT = true;
        }
      } else {
        // Try to parse as number
        const floorNum = parseInt(floorText);
        if (!isNaN(floorNum)) {
          floor = floorNum;
          if (floorNum === 0) {
            isGroundFloor = true;
          }
        } else {
          floor = floorText;
        }
      }
    } else {
      // Fallback to text parsing
      isGroundFloor = /prizemlje|ground|0\s*sprat/i.test(allText);
      isDuplex = /duplex|dupleks|maisonette/i.test(allText);
      isPSUT = /psut|sut/i.test(allText);

      // Try to extract floor number
      const floorMatch = allText.match(/(\d+)\.?\s*sprat/);

      if (isGroundFloor) {
        floor = 0;
      } else if (isPSUT) {
        floor = 'PSUT';
        isExcludedFloor = true;
      } else if (floorMatch) {
        floor = parseInt(floorMatch[1]);
      }
    }

    // Check for duplex in text regardless of plh18
    if (/duplex|dupleks|maisonette/i.test(allText)) {
      isDuplex = true;
    }

    return { floor, isGroundFloor, isDuplex, isPSUT, isExcludedFloor };
  }

  private shouldExcludeApartment(floorInfo: any, description: string): boolean {
    // Exclude apartments with excluded floor types (PR, VPR, PSUT, SUT)
    if (floorInfo.isExcludedFloor) {
      return true;
    }

    // Exclude apartments with PSUT floor (legacy check)
    if (floorInfo.isPSUT) {
      return true;
    }

    // Exclude apartments with "potkrovlje" in description
    if (/potkrovlje/i.test(description)) {
      return true;
    }

    return false;
  }

  private isLedineLocation(locationParts: string[], fullLocation: string): boolean {
    // Check if any location part contains "Ledine"
    const hasLedineInParts = locationParts.some(part =>
      part.toLowerCase().includes('ledine')
    );

    // Also check the full location string
    const hasLedineInFull = fullLocation.toLowerCase().includes('ledine');

    return hasLedineInParts || hasLedineInFull;
  }

  private filterApartments(apartments: Apartment[]): Apartment[] {
    const criteria: FilterCriteria = {
      minSize: this.config.minSize,
      excludeGroundFloor: this.config.excludeGroundFloor,
      excludeDuplex: this.config.excludeDuplex,
      minFloor: 1
    };

    return apartments.filter(apt => {
      if (apt.size < criteria.minSize) return false;
      if (criteria.excludeGroundFloor && apt.isGroundFloor) return false;
      if (criteria.excludeDuplex && apt.isDuplex) return false;

      // Additional exclusions for excluded floor types (PR, VPR, PSUT, SUT)
      if (['PR', 'VPR', 'PSUT', 'SUT'].includes(String(apt.floor))) return false;
      if (apt.description && /potkrovlje/i.test(apt.description)) return false;

      // Exclude Ledine subgroup
      if (this.isLedineLocation(apt.locationParts, apt.fullLocation)) return false;

      return true;
    });
  }

  private generateId(url: string): string {
    // Extract ID from URL or generate from URL hash
    const match = url.match(/\/(\d+)(?:\/|$)/);
    return match ? match[1] : Buffer.from(url).toString('base64').slice(0, 10);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
