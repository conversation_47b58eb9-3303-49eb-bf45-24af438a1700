import axios from 'axios';
import * as cheerio from 'cheerio';
import { Apartment, ScrapingConfig, FilterCriteria } from '../types/apartment';

export class HaloOglasiScraper {
  private config: ScrapingConfig;
  private baseUrl = 'https://www.halooglasi.com';
  private searchUrl = '/nekretnine/prodaja-stanova/beograd';

  constructor(config?: Partial<ScrapingConfig>) {
    this.config = {
      maxPages: 10,
      delayBetweenRequests: 1000,
      minSize: 50,
      excludeGroundFloor: true,
      excludeDuplex: true,
      belgradeOnly: true,
      ...config
    };
  }

  async scrapeApartments(): Promise<Apartment[]> {
    const apartments: Apartment[] = [];
    
    for (let page = 1; page <= this.config.maxPages; page++) {
      console.log(`📄 Scraping page ${page}/${this.config.maxPages}...`);
      
      try {
        const pageApartments = await this.scrapePage(page);
        const filteredApartments = this.filterApartments(pageApartments);
        apartments.push(...filteredApartments);
        
        console.log(`   Found ${pageApartments.length} apartments, ${filteredApartments.length} after filtering`);
        
        // Delay between requests to be respectful
        if (page < this.config.maxPages) {
          await this.delay(this.config.delayBetweenRequests);
        }
      } catch (error) {
        console.error(`❌ Error scraping page ${page}:`, error);
        continue;
      }
    }

    return apartments;
  }

  private async scrapePage(page: number): Promise<Apartment[]> {
    const url = `${this.baseUrl}${this.searchUrl}?page=${page}`;
    
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      const apartments: Apartment[] = [];

      // Find apartment listings - adjust selector based on actual HTML structure
      $('.product-item, .ad-item, .listing-item').each((index, element) => {
        try {
          const apartment = this.parseApartmentElement($, element);
          if (apartment) {
            apartments.push(apartment);
          }
        } catch (error) {
          console.warn(`⚠️  Error parsing apartment ${index}:`, error);
        }
      });

      return apartments;
    } catch (error) {
      console.error(`❌ Error fetching page ${page}:`, error);
      throw error;
    }
  }

  private parseApartmentElement($: cheerio.CheerioAPI, element: cheerio.Element): Apartment | null {
    const $el = $(element);
    
    // Extract basic info - these selectors need to be adjusted based on actual HTML
    const title = $el.find('.product-title, .ad-title, h3 a').text().trim();
    const priceText = $el.find('.price, .product-price').text().trim();
    const locationText = $el.find('.location, .product-location').text().trim();
    const sizeText = $el.find('.surface, .size').text().trim();
    const url = $el.find('a').first().attr('href');

    if (!title || !priceText || !locationText || !url) {
      return null;
    }

    // Parse price (remove EUR, €, and other currency symbols)
    const price = this.parsePrice(priceText);
    if (!price || price <= 0) return null;

    // Parse size
    const size = this.parseSize(sizeText);
    if (!size || size < this.config.minSize) return null;

    // Parse location
    const locationParts = this.parseLocation(locationText);
    if (!this.isValidBelgradeLocation(locationParts)) return null;

    // Parse floor info
    const floorInfo = this.parseFloorInfo($el);
    
    const apartment: Apartment = {
      id: this.generateId(url),
      title,
      price,
      size,
      pricePerSqm: Math.round((price / size) * 100) / 100,
      location: locationParts[locationParts.length - 1] || locationText,
      fullLocation: locationText,
      locationParts,
      floor: floorInfo.floor,
      isGroundFloor: floorInfo.isGroundFloor,
      isDuplex: floorInfo.isDuplex,
      url: url.startsWith('http') ? url : `${this.baseUrl}${url}`
    };

    return apartment;
  }

  private parsePrice(priceText: string): number {
    // Remove all non-digit characters except decimal points
    const cleanPrice = priceText.replace(/[^\d.,]/g, '');
    const price = parseFloat(cleanPrice.replace(',', '.'));
    return isNaN(price) ? 0 : price;
  }

  private parseSize(sizeText: string): number {
    const match = sizeText.match(/(\d+(?:[.,]\d+)?)\s*m²?/i);
    if (match) {
      return parseFloat(match[1].replace(',', '.'));
    }
    return 0;
  }

  private parseLocation(locationText: string): string[] {
    // Split by common delimiters and clean up
    return locationText
      .split(/[-–—,]/)
      .map(part => part.trim())
      .filter(part => part.length > 0);
  }

  private isValidBelgradeLocation(locationParts: string[]): boolean {
    if (!this.config.belgradeOnly) return true;
    
    const locationStr = locationParts.join(' ').toLowerCase();
    return locationStr.includes('beograd') || locationStr.includes('belgrade');
  }

  private parseFloorInfo($el: cheerio.Cheerio<cheerio.Element>): {
    floor: number | string;
    isGroundFloor: boolean;
    isDuplex: boolean;
  } {
    const floorText = $el.find('.floor, .sprat').text().toLowerCase();
    const titleText = $el.find('.product-title, .ad-title').text().toLowerCase();
    const descText = $el.text().toLowerCase();
    
    const allText = `${floorText} ${titleText} ${descText}`;
    
    const isGroundFloor = /prizemlje|ground|0\s*sprat/i.test(allText);
    const isDuplex = /duplex|dupleks|maisonette/i.test(allText);
    
    // Try to extract floor number
    const floorMatch = allText.match(/(\d+)\.?\s*sprat/);
    let floor: number | string = 'unknown';
    
    if (isGroundFloor) {
      floor = 0;
    } else if (floorMatch) {
      floor = parseInt(floorMatch[1]);
    }
    
    return { floor, isGroundFloor, isDuplex };
  }

  private filterApartments(apartments: Apartment[]): Apartment[] {
    const criteria: FilterCriteria = {
      minSize: this.config.minSize,
      excludeGroundFloor: this.config.excludeGroundFloor,
      excludeDuplex: this.config.excludeDuplex,
      minFloor: 1
    };

    return apartments.filter(apt => {
      if (apt.size < criteria.minSize) return false;
      if (criteria.excludeGroundFloor && apt.isGroundFloor) return false;
      if (criteria.excludeDuplex && apt.isDuplex) return false;
      return true;
    });
  }

  private generateId(url: string): string {
    // Extract ID from URL or generate from URL hash
    const match = url.match(/\/(\d+)(?:\/|$)/);
    return match ? match[1] : Buffer.from(url).toString('base64').slice(0, 10);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
