import axios from 'axios';
import * as cheerio from 'cheerio';

async function debugLocation() {
  console.log('🔍 Debugging location extraction...');
  
  try {
    const url = 'https://www.halooglasi.com/nekretnine/prodaja-stanova/beograd';
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      timeout: 10000
    });

    const $ = cheerio.load(response.data);
    
    // Find apartment links
    const apartmentLinks = $('a[href*="/nekretnine/prodaja-stanova/"][href*="?kid="]');
    console.log(`Found ${apartmentLinks.length} apartment links`);
    
    // Check first few apartments for location format
    apartmentLinks.slice(0, 3).each((index, element) => {
      const $link = $(element);
      const title = $link.text().trim();
      const url = $link.attr('href');
      
      // Get container with location info
      let $container = $link;
      for (let level = 0; level < 5; level++) {
        $container = $container.parent();
        const containerText = $container.text().replace(/\s+/g, ' ').trim();
        
        // Look for location pattern
        const locationMatch = containerText.match(/Beograd[^€\d]+/);
        if (locationMatch) {
          const locationText = locationMatch[0].replace(/\s+/g, ' ').trim();
          
          console.log(`\n${index + 1}. ${title}`);
          console.log(`   URL: ${url}`);
          console.log(`   Raw location text: "${locationText}"`);
          
          // Test different parsing approaches
          console.log('   Parsing attempts:');
          
          // Approach 1: Split by " - "
          const dashSplit = locationText.split(' - ').map(p => p.trim()).filter(p => p.length > 0);
          console.log(`   1. Dash split: [${dashSplit.map(p => `"${p}"`).join(', ')}]`);
          
          // Approach 2: New parsing logic
          const parts: string[] = [];
          let cleanLocation = locationText.replace(/^Beograd\s+/, '').trim();

          if (cleanLocation.includes('Opština')) {
            const words = cleanLocation.split(/\s+/);
            const opshtinaIndex = words.findIndex(word => word === 'Opština');

            if (opshtinaIndex >= 0 && opshtinaIndex + 1 < words.length) {
              let municipalityEnd = opshtinaIndex + 2;

              if (municipalityEnd < words.length &&
                  (words[municipalityEnd].toLowerCase() === 'venac' ||
                   words[municipalityEnd].toLowerCase() === 'beograd' ||
                   words[municipalityEnd].toLowerCase() === 'grad')) {
                municipalityEnd++;
              }

              const municipality = words.slice(opshtinaIndex, municipalityEnd).join(' ');
              parts.push(municipality);

              const remaining = words.slice(municipalityEnd);
              if (remaining.length >= 1) parts.push(remaining[0]);
              if (remaining.length >= 2) parts.push(remaining.slice(1).join(' '));
            }
          }
          console.log(`   2. New parsing: [${parts.map(p => `"${p}"`).join(', ')}]`);
          
          break;
        }
      }
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

debugLocation();
