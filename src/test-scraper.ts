import { HaloOglasiScraper } from './scraper/halo-oglasi-scraper';

async function testScraper() {
  console.log('🧪 Testing Halo Oglasi scraper...');

  try {
    // Test with limited pages for quick testing
    const scraper = new HaloOglasiScraper({ maxPages: 1, delayBetweenRequests: 500 });

    console.log('📡 Starting scrape test...');
    const apartments = await scraper.scrapeApartments();

    console.log(`✅ Found ${apartments.length} apartments`);

    // Show first few apartments
    console.log('\n📋 First few apartments:');
    apartments.slice(0, 5).forEach((apt, index) => {
      console.log(`\n${index + 1}. ${apt.title}`);
      console.log(`   💰 Price: €${apt.price.toLocaleString()} (€${apt.pricePerSqm}/m²)`);
      console.log(`   📐 Size: ${apt.size}m²`);
      console.log(`   📍 Location: ${apt.fullLocation}`);
      console.log(`   🏢 Floor: ${apt.floor} (Ground: ${apt.isGroundFloor}, Duplex: ${apt.isDuplex})`);
      console.log(`   🔗 URL: ${apt.url}`);
    });

    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testScraper();
